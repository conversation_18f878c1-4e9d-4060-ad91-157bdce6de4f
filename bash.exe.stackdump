Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF88F940000 ntdll.dll
7FF88EC90000 KERNEL32.DLL
7FF88CCA0000 KERNELBASE.dll
7FF88E6D0000 USER32.dll
7FF88CC70000 win32u.dll
7FF88F8D0000 GDI32.dll
000210040000 msys-2.0.dll
7FF88D5A0000 gdi32full.dll
7FF88D2D0000 msvcp_win.dll
7FF88CA90000 ucrtbase.dll
7FF88D980000 advapi32.dll
7FF88D6E0000 msvcrt.dll
7FF88F820000 sechost.dll
7FF88F6C0000 RPCRT4.dll
7FF88C090000 CRYPTBASE.DLL
7FF88D500000 bcryptPrimitives.dll
7FF88F7E0000 IMM32.DLL
