<?php
// API Configuration for Attrahent Technologies Contact Form

// Email Configuration
define('RECIPIENT_EMAIL', '<EMAIL>');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Attrahent Technologies Website');

// CORS Configuration
define('ALLOWED_ORIGINS', [
    'http://localhost:3000',
    'http://localhost:5173',
    'https://attrahent.com',
    'https://www.attrahent.com',
    'https://attrahent-technologies.netlify.app'
]);

// Rate Limiting (optional)
define('MAX_REQUESTS_PER_HOUR', 10);
define('MAX_REQUESTS_PER_DAY', 50);

// Validation Rules
define('MAX_NAME_LENGTH', 100);
define('MAX_COMPANY_LENGTH', 200);
define('MAX_MESSAGE_LENGTH', 2000);

// Security Settings
define('ENABLE_HONEYPOT', true);
define('ENABLE_RATE_LIMITING', false); // Set to true if you implement rate limiting
define('LOG_SUBMISSIONS', true);

// Email Template Settings
define('EMAIL_TEMPLATE_STYLE', '
    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 8px 8px 0 0;
        text-align: center;
    }
    .header.callback {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    .content {
        background: #f9f9f9;
        padding: 20px;
        border-radius: 0 0 8px 8px;
        border: 1px solid #ddd;
    }
    .field {
        margin-bottom: 15px;
        padding: 10px;
        background: white;
        border-radius: 4px;
        border-left: 4px solid #667eea;
    }
    .field.callback {
        border-left: 4px solid #f5576c;
    }
    .label {
        font-weight: bold;
        color: #555;
        margin-bottom: 5px;
        display: block;
    }
    .value {
        color: #333;
        word-wrap: break-word;
    }
    .footer {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #ddd;
        font-size: 12px;
        color: #666;
        text-align: center;
    }
    .highlight { color: #667eea; font-weight: bold; }
    .highlight.callback { color: #f5576c; font-weight: bold; }
    .priority-notice {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        text-align: center;
        font-weight: bold;
    }
    .callback-notice {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 15px;
        border-radius: 4px;
        margin-bottom: 20px;
        text-align: center;
        font-weight: bold;
    }
');

// Response Messages
define('MESSAGES', [
    'success' => 'Email sent successfully!',
    'invalid_method' => 'Method not allowed',
    'invalid_json' => 'Invalid JSON data',
    'missing_field' => 'Missing required field',
    'invalid_email' => 'Invalid email format',
    'name_too_long' => 'Name is too long (max ' . MAX_NAME_LENGTH . ' characters)',
    'company_too_long' => 'Company name is too long (max ' . MAX_COMPANY_LENGTH . ' characters)',
    'message_too_long' => 'Message is too long (max ' . MAX_MESSAGE_LENGTH . ' characters)',
    'rate_limit_exceeded' => 'Too many requests. Please try again later.',
    'mail_failed' => 'Failed to send email. Please try again later.',
    'honeypot_triggered' => 'Spam detected',
    'server_error' => 'Internal server error'
]);

// Utility Functions
function isOriginAllowed($origin) {
    return in_array($origin, ALLOWED_ORIGINS) ||
           (strpos($origin, 'localhost') !== false &&
            (strpos($origin, ':3000') !== false || strpos($origin, ':5173') !== false));
}

function sanitizeInput($input, $maxLength = null) {
    $sanitized = htmlspecialchars(trim($input));
    if ($maxLength && strlen($sanitized) > $maxLength) {
        return false;
    }
    return $sanitized;
}

function logSubmission($name, $email, $company, $success = true) {
    if (!LOG_SUBMISSIONS) return;

    $status = $success ? 'SUCCESS' : 'FAILED';
    $timestamp = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

    $logEntry = "[$timestamp] $status - $name ($email) from $company - IP: $ip\n";
    error_log($logEntry, 3, 'contact_submissions.log');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

function createEmailSubject($name, $company, $type = 'message') {
    if ($type === 'callback') {
        return "🔥 URGENT: Callback Request from $name - $company";
    }
    return "📧 New Contact Message from $name - $company";
}

function createMessageEmailTemplate($name, $email, $company, $message) {
    return "
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>New Contact Message</title>
    <style>" . EMAIL_TEMPLATE_STYLE . "</style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>📧 New Contact Message</h2>
            <p>Attrahent Technologies Website</p>
        </div>
        <div class='content'>
            <div class='priority-notice'>
                💬 General Inquiry - Standard Response Time: 24 hours
            </div>

            <div class='field'>
                <span class='label'>👤 Name:</span>
                <div class='value'>$name</div>
            </div>
            <div class='field'>
                <span class='label'>📧 Email:</span>
                <div class='value'><a href='mailto:$email' class='highlight'>$email</a></div>
            </div>
            <div class='field'>
                <span class='label'>🏢 Company:</span>
                <div class='value'>$company</div>
            </div>
            <div class='field'>
                <span class='label'>💬 Message:</span>
                <div class='value'>" . ($message ?: 'No message provided') . "</div>
            </div>
        </div>
        <div class='footer'>
            <p><strong>📅 Submitted:</strong> " . date('Y-m-d H:i:s T') . "</p>
            <p><strong>🌐 Source:</strong> Attrahent Technologies Contact Form</p>
            <p><strong>📧 Reply to:</strong> <a href='mailto:$email' class='highlight'>$email</a></p>
            <p><strong>🌍 IP Address:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "</p>
            <p><strong>📋 Type:</strong> General Message</p>
        </div>
    </div>
</body>
</html>
    ";
}

function createCallbackEmailTemplate($name, $email, $company, $message) {
    return "
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>URGENT: Callback Request</title>
    <style>" . EMAIL_TEMPLATE_STYLE . "</style>
</head>
<body>
    <div class='container'>
        <div class='header callback'>
            <h2>🔥 URGENT: Callback Request</h2>
            <p>Attrahent Technologies Website</p>
        </div>
        <div class='content'>
            <div class='callback-notice'>
                📞 CALLBACK REQUESTED - Priority Response Required Within 4 Hours!
            </div>

            <div class='field callback'>
                <span class='label'>👤 Name:</span>
                <div class='value'>$name</div>
            </div>
            <div class='field callback'>
                <span class='label'>📧 Email:</span>
                <div class='value'><a href='mailto:$email' class='highlight callback'>$email</a></div>
            </div>
            <div class='field callback'>
                <span class='label'>🏢 Company:</span>
                <div class='value'>$company</div>
            </div>
            <div class='field callback'>
                <span class='label'>💬 Additional Notes:</span>
                <div class='value'>" . ($message ?: 'No additional notes provided') . "</div>
            </div>

            <div style='background: #e7f3ff; border: 1px solid #b3d9ff; color: #004085; padding: 15px; border-radius: 4px; margin-top: 20px;'>
                <h3 style='margin: 0 0 10px 0; color: #004085;'>📋 Next Steps:</h3>
                <ul style='margin: 0; padding-left: 20px;'>
                    <li>Contact within 4 hours during business hours</li>
                    <li>Use email: <a href='mailto:$email' class='highlight callback'>$email</a></li>
                    <li>Reference: Callback Request #" . date('Ymd-His') . "</li>
                    <li>Priority: HIGH - Potential Sales Lead</li>
                </ul>
            </div>
        </div>
        <div class='footer'>
            <p><strong>📅 Submitted:</strong> " . date('Y-m-d H:i:s T') . "</p>
            <p><strong>🌐 Source:</strong> Attrahent Technologies Contact Form</p>
            <p><strong>📧 Reply to:</strong> <a href='mailto:$email' class='highlight callback'>$email</a></p>
            <p><strong>🌍 IP Address:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "</p>
            <p><strong>📋 Type:</strong> <span style='color: #f5576c; font-weight: bold;'>CALLBACK REQUEST</span></p>
            <p><strong>🎯 Reference:</strong> CB-" . date('Ymd-His') . "</p>
        </div>
    </div>
</body>
</html>
    ";
}
?>
