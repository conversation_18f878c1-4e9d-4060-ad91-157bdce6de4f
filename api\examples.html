<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact API Examples - Attrahent Technologies</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        .container { display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-top: 20px; }
        .form-section { background: #f8f9fa; padding: 20px; border-radius: 8px; border: 2px solid #e9ecef; }
        .form-section.callback { border-color: #f5576c; }
        .form-section.message { border-color: #667eea; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea, select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-weight: bold; }
        .btn-message { background: #667eea; color: white; }
        .btn-callback { background: #f5576c; color: white; }
        .btn:hover { opacity: 0.9; }
        .result { margin-top: 15px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        h1 { text-align: center; color: #333; }
        h2 { color: #667eea; }
        h2.callback { color: #f5576c; }
        .description { background: #e7f3ff; padding: 15px; border-radius: 4px; margin-bottom: 20px; }
        .description.callback { background: #fff5f5; }
        @media (max-width: 768px) {
            .container { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>📧 Contact API Examples</h1>
    <p style="text-align: center; color: #666;">Interactive examples showing both submission types</p>

    <div class="container">
        <!-- General Message Form -->
        <div class="form-section message">
            <h2>📧 Send General Message</h2>
            <div class="description">
                <strong>Use this for:</strong> General inquiries, questions, project discussions, or any non-urgent communication.
                <br><strong>Response Time:</strong> Within 24 hours during business days.
            </div>
            
            <form id="messageForm">
                <div class="form-group">
                    <label for="msg_name">Name *</label>
                    <input type="text" id="msg_name" name="name" required value="Sarah Johnson">
                </div>
                
                <div class="form-group">
                    <label for="msg_email">Email *</label>
                    <input type="email" id="msg_email" name="email" required value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="msg_company">Company *</label>
                    <input type="text" id="msg_company" name="company" required value="Tech Solutions Inc.">
                </div>
                
                <div class="form-group">
                    <label for="msg_message">Message</label>
                    <textarea id="msg_message" name="message" rows="4" placeholder="Tell us about your project or inquiry...">Hi, I'm interested in learning more about your software development services. Could you please send me information about your pricing and typical project timelines?</textarea>
                </div>
                
                <button type="submit" class="btn btn-message">📧 Send Message</button>
            </form>
            
            <div id="messageResult"></div>
        </div>

        <!-- Callback Request Form -->
        <div class="form-section callback">
            <h2 class="callback">📞 Request Urgent Callback</h2>
            <div class="description callback">
                <strong>Use this for:</strong> Urgent matters, immediate project needs, time-sensitive discussions, or when you need to speak with someone quickly.
                <br><strong>Response Time:</strong> Within 4 hours during business hours (priority handling).
            </div>
            
            <form id="callbackForm">
                <div class="form-group">
                    <label for="cb_name">Name *</label>
                    <input type="text" id="cb_name" name="name" required value="Michael Chen">
                </div>
                
                <div class="form-group">
                    <label for="cb_email">Email *</label>
                    <input type="email" id="cb_email" name="email" required value="<EMAIL>">
                </div>
                
                <div class="form-group">
                    <label for="cb_company">Company *</label>
                    <input type="text" id="cb_company" name="company" required value="Urgent Project Corp">
                </div>
                
                <div class="form-group">
                    <label for="cb_message">Additional Notes (Optional)</label>
                    <textarea id="cb_message" name="message" rows="4" placeholder="Any specific requirements or preferred callback time...">We have an urgent project that needs to start next week. Please call me today to discuss requirements and timeline. Available between 9 AM - 6 PM EST.</textarea>
                </div>
                
                <button type="submit" class="btn btn-callback">📞 Request Callback</button>
            </form>
            
            <div id="callbackResult"></div>
        </div>
    </div>

    <hr style="margin: 40px 0;">
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
        <h3>📋 How It Works</h3>
        <ol>
            <li><strong>Choose the right form:</strong> Use "Send Message" for general inquiries or "Request Callback" for urgent matters</li>
            <li><strong>Fill out the form:</strong> All forms require name, email, and company. Message is optional but recommended</li>
            <li><strong>Submit:</strong> The form data is sent to our PHP API endpoint</li>
            <li><strong>Email sent:</strong> Different email templates are used based on submission type</li>
            <li><strong>Response:</strong> You'll receive confirmation and we'll respond according to the priority level</li>
        </ol>
        
        <h3>🎨 Email Template Differences</h3>
        <ul>
            <li><strong>General Message:</strong> Blue theme, standard priority, 24-hour response commitment</li>
            <li><strong>Callback Request:</strong> Red theme, high priority, 4-hour response commitment, includes action items</li>
        </ul>
    </div>

    <script>
        async function submitForm(formData, type, resultElementId) {
            const resultDiv = document.getElementById(resultElementId);
            
            // Add type to form data
            formData.type = type;
            
            // Show loading state
            const loadingMessage = type === 'callback' ? 
                '📞 Sending callback request...' : 
                '📧 Sending message...';
            resultDiv.innerHTML = `<div class="result loading">🔄 ${loadingMessage}</div>`;
            
            try {
                const response = await fetch('contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    const successIcon = result.type === 'callback' ? '📞' : '📧';
                    const typeLabel = result.type === 'callback' ? 'Callback Request' : 'Message';
                    
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <strong>${successIcon} ${typeLabel} Sent Successfully!</strong><br>
                            ${result.message}<br>
                            <small>Sent at: ${new Date(result.timestamp).toLocaleString()}</small>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <strong>❌ Error</strong><br>
                            ${result.message}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <strong>❌ Network Error</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        // Handle message form submission
        document.getElementById('messageForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            await submitForm(data, 'message', 'messageResult');
        });

        // Handle callback form submission
        document.getElementById('callbackForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const data = Object.fromEntries(formData.entries());
            await submitForm(data, 'callback', 'callbackResult');
        });
    </script>
</body>
</html>
