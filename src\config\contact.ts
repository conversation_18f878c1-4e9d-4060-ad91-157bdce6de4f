// Contact information configuration
// Update these values to change contact details across the entire application

export const contactConfig = {
  // Email addresses
  email: {
    primary: '<EMAIL>',
    support: '<EMAIL>',
    sales: '<EMAIL>',
    testEmail: '<EMAIL>',
  },

  // Phone numbers
  phone: {
    primary: '+919836380086',
    international: '+919836380086',
    whatsapp: '+919836380086',
  },

  // Social media profiles
  social: {
    linkedin: 'https://linkedin.com/company/attrahent-technologies',
    twitter: 'https://twitter.com/attrahent',
    github: 'https://github.com/attrahent',
    facebook: 'https://facebook.com/attrahent',
  },

  // Business addresses
  addresses: {
    headquarters: {
      street: '123 Innovation Drive',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA',
      full: '123 Innovation Drive, San Francisco, CA 94105, USA',
    },
    uk: {
      street: '45 Tech Square',
      city: 'London',
      state: 'England',
      zipCode: 'EC2A 4DN',
      country: 'UK',
      full: '45 Tech Square, London, EC2A 4DN, UK',
    },
  },

  // Business hours
  businessHours: {
    timezone: 'PST',
    weekdays: '9:00 AM - 6:00 PM',
    weekends: 'Closed',
    support: '24/7 Emergency Support Available',
  },

  // Quick contact methods with display formatting
  quickContact: {
    email: {
      value: '<EMAIL>',
      display: '<EMAIL>',
      href: 'mailto:<EMAIL>',
    },
    phone: {
      value: '+919836380086',
      display: '+919836380086',
      href: 'tel:+919836380086',
    },
    whatsapp: {
      value: '+919836380086',
      display: '+919836380086',
      href: 'https://wa.me/919836380086',
    },
    linkedin: {
      value: 'https://linkedin.com/company/attrahent-technologies',
      display: 'LinkedIn',
      href: 'https://linkedin.com/company/attrahent-technologies',
    },
  },
} as const;

// Helper functions for common contact operations
export const getContactHref = (type: 'email' | 'phone' | 'whatsapp' | 'linkedin') => {
  return contactConfig.quickContact[type].href;
};

export const getContactDisplay = (type: 'email' | 'phone' | 'whatsapp' | 'linkedin') => {
  return contactConfig.quickContact[type].display;
};

export const getContactValue = (type: 'email' | 'phone' | 'whatsapp' | 'linkedin') => {
  return contactConfig.quickContact[type].value;
};
