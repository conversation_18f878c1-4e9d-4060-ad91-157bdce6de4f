import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const caseStudies = [
    {
      name: '<PERSON>',
      role: 'VP of Engineering',
      company: 'DataFlow Solutions',
      challenge: 'Manual data processing taking 48 hours, limiting real-time decision making',
      solution:
        'AI-powered analytics platform with real-time data processing and predictive insights',
      outcome:
        'Real-time insights reduced operational costs by 40% and improved customer satisfaction',
      impact: '40% cost reduction',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: '<PERSON>',
      role: 'Chief Digital Officer',
      company: 'Global Enterprises',
      challenge: 'Rapid growth overwhelming existing systems, security vulnerabilities exposed',
      solution: 'Scalable cloud-native platform with enterprise-grade security and compliance',
      outcome: 'Seamless scaling to 10x user base with zero security incidents',
      impact: '10x user scaling',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'David Kim',
      role: 'Head of Innovation',
      company: 'FutureTech Corp',
      challenge: '18-month development timeline threatening market opportunity',
      solution: 'Agile development with rapid prototyping and MVP-first approach',
      outcome: 'Product launched 6 months ahead of schedule with zero quality compromises',
      impact: '6 months faster to market',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'Sarah Johnson',
      role: 'CTO',
      company: 'HealthTech Innovations',
      challenge: 'Legacy healthcare system causing patient data delays and compliance issues',
      solution: 'HIPAA-compliant cloud platform with real-time patient data synchronization',
      outcome: 'Improved patient care delivery and achieved 100% compliance certification',
      impact: '95% faster data access',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'Alex Chen',
      role: 'Founder & CEO',
      company: 'EcoSmart Solutions',
      challenge: 'Manual inventory tracking causing 30% revenue loss and customer dissatisfaction',
      solution: 'IoT-enabled smart inventory management with predictive analytics',
      outcome: 'Eliminated stockouts and increased customer satisfaction by 85%',
      impact: '30% revenue increase',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'Maria Santos',
      role: 'Head of Digital',
      company: 'RetailMax Group',
      challenge: 'Outdated e-commerce platform limiting mobile sales and user experience',
      solution: 'Modern PWA with AI-powered personalization and mobile-first design',
      outcome: 'Mobile sales increased 200% with improved conversion rates',
      impact: '200% mobile sales boost',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1181424/pexels-photo-1181424.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
  ];

  // Get items per view based on screen size
  const getItemsPerView = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1024) return 3; // Desktop: 3 items
      if (window.innerWidth >= 768) return 3;  // Tablet: 3 items
      return 2; // Mobile: 2 items
    }
    return 3; // Default
  };

  const [itemsPerView, setItemsPerView] = useState(getItemsPerView());

  useEffect(() => {
    const handleResize = () => {
      setItemsPerView(getItemsPerView());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const maxIndex = Math.max(0, caseStudies.length - itemsPerView);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex >= maxIndex ? 0 : prevIndex + 1));
    }, 5000);

    return () => clearInterval(timer);
  }, [maxIndex]);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex >= maxIndex ? 0 : prevIndex + 1));
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex <= 0 ? maxIndex : prevIndex - 1));
  };

  return (
    <section id="testimonials" className="py-24 bg-white dark:bg-secondary-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Case Studies
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            Real projects, real results. See how we've helped businesses transform and grow.
          </p>
        </motion.div>

        <div className="relative">
          {/* Multi-item carousel */}
          <div className="relative overflow-hidden">
            <motion.div
              className="flex transition-transform duration-500 ease-in-out"
              style={{
                transform: `translateX(-${currentIndex * (100 / itemsPerView)}%)`,
              }}
            >
              {caseStudies.map((caseStudy, index) => (
                <motion.div
                  key={index}
                  className={`flex-shrink-0 px-3 ${
                    itemsPerView === 2 ? 'w-1/2' : 'w-1/3'
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                >
                  <div className="bg-white dark:bg-secondary-800 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 h-full">


                    {/* Case Study Content */}
                    <div className="space-y-4 mb-6">
                      {/* Challenge */}
                      <div>
                        <h4 className="text-xs font-semibold text-red-600 dark:text-red-400 mb-1 uppercase tracking-wide">
                          Challenge
                        </h4>
                        <p className="text-sm text-secondary-700 dark:text-secondary-300 leading-relaxed line-clamp-2">
                          {caseStudy.challenge}
                        </p>
                      </div>

                      {/* Solution */}
                      <div>
                        <h4 className="text-xs font-semibold text-blue-600 dark:text-blue-400 mb-1 uppercase tracking-wide">
                          Solution
                        </h4>
                        <p className="text-sm text-secondary-700 dark:text-secondary-300 leading-relaxed line-clamp-2">
                          {caseStudy.solution}
                        </p>
                      </div>

                      {/* Impact highlight */}
                      <div className="bg-accent-50 dark:bg-accent-950/30 rounded-lg p-3 text-center">
                        <div className="text-lg font-bold text-accent-600 dark:text-accent-400">
                          {caseStudy.impact}
                        </div>
                        <div className="text-xs text-accent-700 dark:text-accent-300">
                          Quantified Impact
                        </div>
                      </div>
                    </div>


                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Navigation buttons */}
            <button
              onClick={prevTestimonial}
              disabled={currentIndex === 0}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/90 dark:bg-secondary-800/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white dark:hover:bg-secondary-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              type="button"
              aria-label="Previous testimonials"
            >
              <ChevronLeft className="w-6 h-6 text-secondary-700 dark:text-secondary-300" />
            </button>

            <button
              onClick={nextTestimonial}
              disabled={currentIndex >= maxIndex}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/90 dark:bg-secondary-800/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white dark:hover:bg-secondary-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              type="button"
              aria-label="Next testimonials"
            >
              <ChevronRight className="w-6 h-6 text-secondary-700 dark:text-secondary-300" />
            </button>
          </div>

          {/* Carousel indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {Array.from({ length: maxIndex + 1 }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-primary-600 dark:bg-primary-400 w-8'
                    : 'bg-secondary-300 dark:bg-secondary-600 hover:bg-secondary-400 dark:hover:bg-secondary-500'
                }`}
                aria-label={`View testimonials page ${index + 1}`}
                type="button"
              />
            ))}
          </div>
        </div>

        {/* See More Case Studies - Hidden for now */}
        {/* <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <motion.a
            href="#"
            whileHover={{ scale: 1.05 }}
            className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-200"
          >
            See More Success Stories
          </motion.a>
        </motion.div> */}

        {/* Client logos grid - Hidden for now */}
        {/* <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20"
        >
          <h3 className="text-center text-lg font-semibold text-secondary-600 dark:text-secondary-400 mb-8">
            Trusted by 500+ companies worldwide
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60 dark:opacity-40">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                whileHover={{ scale: 1.1, opacity: 1 }}
                className="flex items-center justify-center h-16"
              >
                <div className="w-24 h-8 bg-secondary-200 dark:bg-secondary-700 rounded-lg" />
              </motion.div>
            ))}
          </div>
        </motion.div> */}
      </div>
    </section>
  );
};

export default Testimonials;
