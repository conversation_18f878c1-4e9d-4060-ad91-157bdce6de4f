import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Star, Quote } from 'lucide-react';

const Testimonials: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const caseStudies = [
    {
      name: '<PERSON>',
      role: 'CTO',
      company: 'TechForward Inc.',
      challenge: 'Legacy infrastructure causing 60% downtime and limiting scalability',
      solution: 'Cloud-native migration with microservices architecture and automated deployment',
      outcome: '300% improvement in system performance, 99.9% uptime achieved',
      impact: '300% performance boost',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: '<PERSON>',
      role: 'VP of Engineering',
      company: 'DataFlow Solutions',
      challenge: 'Manual data processing taking 48 hours, limiting real-time decision making',
      solution:
        'AI-powered analytics platform with real-time data processing and predictive insights',
      outcome:
        'Real-time insights reduced operational costs by 40% and improved customer satisfaction',
      impact: '40% cost reduction',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'Emily Watson',
      role: 'Chief Digital Officer',
      company: 'Global Enterprises',
      challenge: 'Rapid growth overwhelming existing systems, security vulnerabilities exposed',
      solution: 'Scalable cloud-native platform with enterprise-grade security and compliance',
      outcome: 'Seamless scaling to 10x user base with zero security incidents',
      impact: '10x user scaling',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1181686/pexels-photo-1181686.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
    {
      name: 'David Kim',
      role: 'Head of Innovation',
      company: 'FutureTech Corp',
      challenge: '18-month development timeline threatening market opportunity',
      solution: 'Agile development with rapid prototyping and MVP-first approach',
      outcome: 'Product launched 6 months ahead of schedule with zero quality compromises',
      impact: '6 months faster to market',
      rating: 5,
      avatar:
        'https://images.pexels.com/photos/1043471/pexels-photo-1043471.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop',
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % caseStudies.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [caseStudies.length]);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % caseStudies.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? caseStudies.length - 1 : prevIndex - 1));
  };

  return (
    <section id="testimonials" className="py-24 bg-white dark:bg-secondary-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Success Stories
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            Real projects, real results. See how we've helped businesses transform and grow.
          </p>
        </motion.div>

        <div className="relative max-w-4xl mx-auto">
          {/* Main testimonial display */}
          <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/30 dark:to-accent-950/30 p-8 md:p-12">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="text-center"
              >
                {/* Quote icon */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2 }}
                  className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full mb-8"
                >
                  <Quote className="w-8 h-8 text-primary-600 dark:text-primary-400" />
                </motion.div>

                {/* Case Study Content */}
                <div className="space-y-6 mb-8">
                  {/* Challenge */}
                  <div className="text-left">
                    <h4 className="text-sm font-semibold text-red-600 dark:text-red-400 mb-2 uppercase tracking-wide">
                      Challenge
                    </h4>
                    <p className="text-secondary-700 dark:text-secondary-300 leading-relaxed">
                      {caseStudies[currentIndex].challenge}
                    </p>
                  </div>

                  {/* Solution */}
                  <div className="text-left">
                    <h4 className="text-sm font-semibold text-blue-600 dark:text-blue-400 mb-2 uppercase tracking-wide">
                      Solution
                    </h4>
                    <p className="text-secondary-700 dark:text-secondary-300 leading-relaxed">
                      {caseStudies[currentIndex].solution}
                    </p>
                  </div>

                  {/* Outcome */}
                  <div className="text-left">
                    <h4 className="text-sm font-semibold text-green-600 dark:text-green-400 mb-2 uppercase tracking-wide">
                      Outcome
                    </h4>
                    <p className="text-secondary-700 dark:text-secondary-300 leading-relaxed">
                      {caseStudies[currentIndex].outcome}
                    </p>
                  </div>

                  {/* Impact highlight */}
                  <div className="bg-accent-50 dark:bg-accent-950/30 rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-accent-600 dark:text-accent-400">
                      {caseStudies[currentIndex].impact}
                    </div>
                    <div className="text-sm text-accent-700 dark:text-accent-300">
                      Quantified Impact
                    </div>
                  </div>
                </div>

                {/* Rating */}
                <div className="flex justify-center mb-6">
                  {[...Array(caseStudies[currentIndex].rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-accent-400 fill-current" />
                  ))}
                </div>

                {/* Author info */}
                <div className="flex items-center justify-center space-x-4">
                  <motion.img
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.3 }}
                    src={caseStudies[currentIndex].avatar}
                    alt={caseStudies[currentIndex].name}
                    className="w-16 h-16 rounded-full object-cover border-4 border-white dark:border-secondary-700 shadow-lg"
                  />
                  <div className="text-left">
                    <div className="font-bold text-secondary-900 dark:text-white">
                      {caseStudies[currentIndex].name}
                    </div>
                    <div className="text-secondary-600 dark:text-secondary-400">
                      {caseStudies[currentIndex].role}
                    </div>
                    <div className="text-primary-600 dark:text-primary-400 font-medium">
                      {caseStudies[currentIndex].company}
                    </div>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Navigation buttons */}
            <button
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white dark:hover:bg-secondary-700 transition-colors duration-200"
              type="button"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="w-6 h-6 text-secondary-700 dark:text-secondary-300" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-full shadow-lg hover:bg-white dark:hover:bg-secondary-700 transition-colors duration-200"
              type="button"
              aria-label="Next testimonial"
            >
              <ChevronRight className="w-6 h-6 text-secondary-700 dark:text-secondary-300" />
            </button>
          </div>

          {/* Case study indicators */}
          <div className="flex justify-center mt-8 space-x-2">
            {caseStudies.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex
                    ? 'bg-primary-600 dark:bg-primary-400 w-8'
                    : 'bg-secondary-300 dark:bg-secondary-600 hover:bg-secondary-400 dark:hover:bg-secondary-500'
                }`}
                aria-label={`View case study ${index + 1}`}
                type="button"
              />
            ))}
          </div>
        </div>

        {/* See More Success Stories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-12"
        >
          <motion.a
            href="#"
            whileHover={{ scale: 1.05 }}
            className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-200"
          >
            See More Success Stories
          </motion.a>
        </motion.div>

        {/* Client logos grid */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20"
        >
          <h3 className="text-center text-lg font-semibold text-secondary-600 dark:text-secondary-400 mb-8">
            Trusted by 500+ companies worldwide
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60 dark:opacity-40">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                whileHover={{ scale: 1.1, opacity: 1 }}
                className="flex items-center justify-center h-16"
              >
                <div className="w-24 h-8 bg-secondary-200 dark:bg-secondary-700 rounded-lg" />
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Testimonials;
