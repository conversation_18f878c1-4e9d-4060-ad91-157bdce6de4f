import React from 'react';
import { motion } from 'framer-motion';
import { Users, Globe, TrendingUp, Star, CheckCircle } from 'lucide-react';

const TrustSignals: React.FC = () => {
  const stats = [
    {
      icon: Users,
      value: '18+',
      label: 'Years in Business',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: CheckCircle,
      value: '500+',
      label: 'Projects Delivered',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Globe,
      value: '25+',
      label: 'Countries Served',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: TrendingUp,
      value: '98%',
      label: 'Client Satisfaction',
      color: 'from-orange-500 to-red-500',
    },
  ];

  const clientLogos = [
    'DataFlow Solutions',
    'Global Enterprises',
    'FutureTech Corp',
    'Innovation Labs',
    'Digital Dynamics',
  ];

  const testimonialQuotes = [
    {
      quote:
        'Attrahent delivered our mobile platform in record time—seamless process, outstanding support.',
      author: 'CTO, Fintech Startup',
      rating: 5,
    },
    {
      quote: 'The AI-powered analytics platform has revolutionized how we make decisions.',
      author: 'VP of Engineering, DataFlow Solutions',
      rating: 5,
    },
    {
      quote: 'Working with Attrahent has been a game-changer for our digital transformation.',
      author: 'Chief Digital Officer, Global Enterprises',
      rating: 5,
    },
  ];

  return (
    <section className="py-16 bg-white dark:bg-secondary-900 border-b border-secondary-100 dark:border-secondary-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h2 className="text-2xl font-bold text-secondary-900 dark:text-white mb-4">
            Trusted by innovative companies worldwide
          </h2>
        </motion.div>

        {/* Key Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
        >
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center"
            >
              <div
                className={`w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br ${stat.color} p-4 shadow-lg`}
              >
                <stat.icon className="w-full h-full text-white" />
              </div>
              <div className="text-3xl font-bold text-secondary-900 dark:text-white mb-2">
                {stat.value}
              </div>
              <div className="text-secondary-600 dark:text-secondary-400 text-sm">{stat.label}</div>
            </motion.div>
          ))}
        </motion.div>

        {/* Client Logos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-16"
        >
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center opacity-60 dark:opacity-40">
            {clientLogos.map((logo, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: 1.05, opacity: 1 }}
                className="flex items-center justify-center h-16 bg-secondary-50 dark:bg-secondary-800 rounded-lg p-4"
              >
                <div className="text-xs font-medium text-secondary-600 dark:text-secondary-400 text-center">
                  {logo}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Rotating Testimonials */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="grid md:grid-cols-3 gap-6"
        >
          {testimonialQuotes.map((testimonial, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
              className="bg-secondary-50 dark:bg-secondary-800 rounded-xl p-6"
            >
              {/* Rating */}
              <div className="flex mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 text-accent-400 fill-current" />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-secondary-700 dark:text-secondary-300 mb-4 text-sm leading-relaxed">
                "{testimonial.quote}"
              </blockquote>

              {/* Author */}
              <div className="text-xs text-secondary-600 dark:text-secondary-400 font-medium">
                — {testimonial.author}
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default TrustSignals;
