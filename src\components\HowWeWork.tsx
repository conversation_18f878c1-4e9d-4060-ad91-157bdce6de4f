import React from 'react';
import { motion } from 'framer-motion';
import { Search, Lightbulb, Code, CheckCircle, Headphones, ArrowRight } from 'lucide-react';

const HowWeWork: React.FC = () => {
  const processSteps = [
    {
      icon: Search,
      title: 'Discovery & Consultation',
      description:
        'We dive deep into your business needs, challenges, and goals to understand the perfect solution for your unique requirements.',
      color: 'from-blue-500 to-cyan-500',
      step: '01',
    },
    {
      icon: Lightbulb,
      title: 'Solution Design',
      description:
        'Our experts craft a comprehensive technical architecture and project roadmap tailored to your specific objectives.',
      color: 'from-green-500 to-emerald-500',
      step: '02',
    },
    {
      icon: Code,
      title: 'Agile Development',
      description:
        'We build your solution using proven agile methodologies with regular sprints, demos, and feedback cycles.',
      color: 'from-purple-500 to-pink-500',
      step: '03',
    },
    {
      icon: CheckCircle,
      title: 'QA & Launch',
      description:
        'Rigorous testing and quality assurance ensure a smooth, successful launch with minimal disruption to your operations.',
      color: 'from-orange-500 to-red-500',
      step: '04',
    },
    {
      icon: Headphones,
      title: 'Ongoing Support',
      description:
        'Continuous monitoring, maintenance, and enhancement to ensure your solution evolves with your business needs.',
      color: 'from-indigo-500 to-blue-500',
      step: '05',
    },
  ];

  return (
    <section className="py-24 bg-secondary-50 dark:bg-secondary-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Our Process
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            A proven methodology that ensures successful delivery from concept to launch and beyond.
          </p>
        </motion.div>

        {/* Process steps */}
        <div className="relative">
          {/* Connection line for desktop */}
          <div className="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-primary-200 via-accent-200 to-primary-200 dark:from-primary-800 dark:via-accent-800 dark:to-primary-800" />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {processSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="relative group"
              >
                {/* Step card */}
                <div className="bg-white dark:bg-secondary-800 rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:-translate-y-2 relative z-10">
                  {/* Step number */}
                  <div className="absolute -top-4 left-8">
                    <div
                      className={`w-8 h-8 rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center text-white text-sm font-bold shadow-lg`}
                    >
                      {step.step}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="mb-6 mt-4">
                    <div
                      className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${step.color} p-4 shadow-lg group-hover:scale-110 transition-transform duration-300 mx-auto`}
                    >
                      <step.icon className="w-full h-full text-white" />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-bold text-secondary-900 dark:text-white mb-4 text-center group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                    {step.title}
                  </h3>

                  <p className="text-secondary-600 dark:text-secondary-300 leading-relaxed text-center text-sm">
                    {step.description}
                  </p>
                </div>

                {/* Arrow connector for desktop */}
                {index < processSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-24 -right-4 z-20">
                    <motion.div
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 2, repeat: Infinity, ease: 'easeInOut' }}
                      className="w-8 h-8 bg-white dark:bg-secondary-800 rounded-full flex items-center justify-center shadow-lg border-2 border-primary-200 dark:border-primary-800"
                    >
                      <ArrowRight className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                    </motion.div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-primary-600 to-accent-600 rounded-3xl p-8 md:p-12 text-white">
            <h3 className="text-2xl font-bold mb-4">Ready to start your project?</h3>
            <p className="text-primary-100 mb-8 max-w-2xl mx-auto">
              Let's discuss your requirements and create a custom solution that drives your business
              forward.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.location.href = '#contact'}
              className="px-8 py-4 bg-white text-primary-600 font-semibold rounded-lg hover:bg-primary-50 transition-all duration-200"
            >
              Schedule a Consultation
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default HowWeWork;
