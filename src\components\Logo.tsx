import React from 'react';
import { motion } from 'framer-motion';

import logoImage from '../assets/logo.png';

interface LogoProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const Logo: React.FC<LogoProps> = ({ className = '', showText = false, size = 'md' }) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-12',
  };

  const handleLogoClick = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <motion.button
      onClick={handleLogoClick}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={`flex items-center space-x-3 cursor-pointer ${className}`}
    >
      <img src={logoImage} alt="Attrahent Technologies" className={`w-auto ${sizeClasses[size]}`} />
      {showText && (
        <div className="flex flex-col">
          <span className="text-lg font-bold text-secondary-900 dark:text-white">Attrahent</span>
          <span className="text-xs text-secondary-600 dark:text-secondary-400">Technologies</span>
        </div>
      )}
    </motion.button>
  );
};

export default Logo;
