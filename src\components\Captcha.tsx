import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface CaptchaProps {
  onVerify: (isVerified: boolean) => void;
}

const Captcha: React.FC<CaptchaProps> = ({ onVerify }) => {
  const [captchaValue, setCaptchaValue] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    generateCaptcha();
  }, []);

  const generateCaptcha = () => {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaValue(result);
    setInputValue('');
    setError('');
    onVerify(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setError('');
  };

  const handleVerify = () => {
    if (inputValue.toLowerCase().trim() === captchaValue.toLowerCase()) {
      onVerify(true);
      setError('CAPTCHA verified successfully!');
    } else {
      onVerify(false);
      setError('Incorrect CAPTCHA. Please try again.');
      generateCaptcha(); // Regenerate CAPTCHA on failure
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-3 sm:p-4 space-y-3 sm:space-y-4"
    >
      <p className="text-white/80 text-sm sm:text-base text-center sm:text-left">Please verify you are not a robot:</p>

      {/* Mobile-first responsive layout */}
      <div className="space-y-3 sm:space-y-0">
        {/* CAPTCHA display - full width on mobile, centered */}
        <div className="flex justify-center">
          <div className="bg-white/20 px-4 sm:px-6 py-3 sm:py-2 rounded-lg text-white text-xl sm:text-2xl font-bold tracking-widest select-none min-w-[140px] sm:w-32 text-center border border-white/10">
            {captchaValue}
          </div>
        </div>

        {/* Input and buttons container */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4">
          {/* CAPTCHA input - full width on mobile */}
          <input
            type="text"
            value={inputValue}
            onChange={handleChange}
            placeholder="Enter CAPTCHA"
            className="flex-grow px-4 py-3 sm:py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent text-center sm:text-left text-lg sm:text-base"
          />

          {/* Action buttons - stacked on mobile, inline on desktop */}
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <motion.button
              type="button"
              onClick={handleVerify}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 sm:px-6 py-3 sm:py-2 bg-primary-600 hover:bg-primary-700 active:bg-primary-800 text-white font-semibold rounded-lg transition-all duration-200 text-sm sm:text-base min-h-[44px] sm:min-h-[auto]"
            >
              Verify
            </motion.button>
            <motion.button
              type="button"
              onClick={generateCaptcha}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-4 py-3 sm:py-2 bg-gray-600 hover:bg-gray-700 active:bg-gray-800 text-white font-semibold rounded-lg transition-all duration-200 text-sm sm:text-base min-h-[44px] sm:min-h-[auto]"
            >
              Refresh
            </motion.button>
          </div>
        </div>
      </div>

      {/* Error message with better mobile styling */}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`text-sm sm:text-base text-center sm:text-left ${
            error.includes('successfully') ? 'text-green-400' : 'text-red-400'
          }`}
        >
          {error}
        </motion.p>
      )}
    </motion.div>
  );
};

export default Captcha;
