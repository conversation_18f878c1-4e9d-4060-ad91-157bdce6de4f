import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

interface CaptchaProps {
  onVerify: (isVerified: boolean) => void;
}

const Captcha: React.FC<CaptchaProps> = ({ onVerify }) => {
  const [captchaValue, setCaptchaValue] = useState('');
  const [inputValue, setInputValue] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    generateCaptcha();
  }, []);

  const generateCaptcha = () => {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setCaptchaValue(result);
    setInputValue('');
    setError('');
    onVerify(false);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setError('');
  };

  const handleVerify = () => {
    if (inputValue.toLowerCase().trim() === captchaValue.toLowerCase()) {
      onVerify(true);
      setError('CAPTCHA verified successfully!');
    } else {
      onVerify(false);
      setError('Incorrect CAPTCHA. Please try again.');
      generateCaptcha(); // Regenerate CAPTCHA on failure
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4 space-y-3"
    >
      <p className="text-white/80 text-sm">Please verify you are not a robot:</p>
      <div className="flex items-center space-x-4">
        <div className="bg-white/20 px-4 py-2 rounded-lg text-white text-2xl font-bold tracking-widest select-none w-32 text-center">
          {captchaValue}
        </div>
        <input
          type="text"
          value={inputValue}
          onChange={handleChange}
          placeholder="Enter CAPTCHA"
          className="flex-grow px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400"
        />
        <motion.button
          type="button"
          onClick={handleVerify}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-200"
        >
          Verify
        </motion.button>
        <motion.button
          type="button"
          onClick={generateCaptcha}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-lg transition-all duration-200"
        >
          Refresh
        </motion.button>
      </div>
      {error && <p className="text-red-400 text-sm">{error}</p>}
    </motion.div>
  );
};

export default Captcha;