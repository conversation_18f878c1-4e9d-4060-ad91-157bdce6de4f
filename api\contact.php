<?php
// Include configuration
require_once 'config.php';

// Set CORS headers
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (isOriginAllowed($origin)) {
    header("Access-Control-Allow-Origin: $origin");
} else {
    header('Access-Control-Allow-Origin: *');
}
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_method']]);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate input
if (!$data) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_json']]);
    exit();
}

// Check for honeypot field (spam protection)
if (ENABLE_HONEYPOT && !empty($data['website'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['honeypot_triggered']]);
    exit();
}

// Required fields validation
$required_fields = ['name', 'email', 'company'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => MESSAGES['missing_field'] . ": $field"]);
        exit();
    }
}

// Sanitize and validate input data
$name = sanitizeInput($data['name'], MAX_NAME_LENGTH);
$email = filter_var(trim($data['email']), FILTER_SANITIZE_EMAIL);
$company = sanitizeInput($data['company'], MAX_COMPANY_LENGTH);
$message = sanitizeInput($data['message'] ?? '', MAX_MESSAGE_LENGTH);

// Validation checks
if ($name === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['name_too_long']]);
    exit();
}

if ($company === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['company_too_long']]);
    exit();
}

if ($message === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['message_too_long']]);
    exit();
}

if (!validateEmail($email)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_email']]);
    exit();
}

// Create email subject
$subject = createEmailSubject($name, $company);

// Create HTML email template
$html_body = "
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>New Contact Form Submission</title>
    <style>" . EMAIL_TEMPLATE_STYLE . "</style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h2>🚀 New Contact Form Submission</h2>
            <p>Attrahent Technologies Website</p>
        </div>
        <div class='content'>
            <div class='field'>
                <span class='label'>👤 Name:</span>
                <div class='value'>$name</div>
            </div>
            <div class='field'>
                <span class='label'>📧 Email:</span>
                <div class='value'><a href='mailto:$email' class='highlight'>$email</a></div>
            </div>
            <div class='field'>
                <span class='label'>🏢 Company:</span>
                <div class='value'>$company</div>
            </div>
            <div class='field'>
                <span class='label'>💬 Message:</span>
                <div class='value'>" . ($message ?: 'No message provided') . "</div>
            </div>
        </div>
        <div class='footer'>
            <p><strong>📅 Submitted:</strong> " . date('Y-m-d H:i:s T') . "</p>
            <p><strong>🌐 Source:</strong> Attrahent Technologies Contact Form</p>
            <p><strong>📧 Reply to:</strong> <a href='mailto:$email' class='highlight'>$email</a></p>
            <p><strong>🌍 IP Address:</strong> " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "</p>
        </div>
    </div>
</body>
</html>
";

// Create plain text version
$text_body = "
NEW CONTACT FORM SUBMISSION - Attrahent Technologies
==================================================

Name: $name
Email: $email
Company: $company
Message: " . ($message ?: 'No message provided') . "

Submitted: " . date('Y-m-d H:i:s T') . "
Source: Attrahent Technologies Contact Form
Reply to: $email

--
This email was sent from the Attrahent Technologies website contact form.
";

// Email headers
$headers = [
    'MIME-Version: 1.0',
    'Content-Type: text/html; charset=UTF-8',
    'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
    "Reply-To: $name <$email>",
    'X-Mailer: PHP/' . phpversion(),
    'X-Priority: 3',
    'X-MSMail-Priority: Normal',
    'X-Originating-IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown')
];

// Attempt to send email
try {
    $mail_sent = mail(RECIPIENT_EMAIL, $subject, $html_body, implode("\r\n", $headers));

    if ($mail_sent) {
        // Log successful submission
        logSubmission($name, $email, $company, true);

        // Return success response
        echo json_encode([
            'success' => true,
            'message' => MESSAGES['success'],
            'timestamp' => date('c'),
            'data' => [
                'name' => $name,
                'email' => $email,
                'company' => $company
            ]
        ]);
    } else {
        throw new Exception('Mail function returned false');
    }

} catch (Exception $e) {
    // Log error
    logSubmission($name, $email, $company, false);
    error_log("Failed to send contact form email: " . $e->getMessage());

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => MESSAGES['mail_failed'],
        'error' => 'Mail delivery failed',
        'timestamp' => date('c')
    ]);
}
?>
