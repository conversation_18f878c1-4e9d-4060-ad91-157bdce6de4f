<?php
// Include configuration
require_once 'config.php';

// Set CORS headers
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';
if (isOriginAllowed($origin)) {
    header("Access-Control-Allow-Origin: $origin");
} else {
    header('Access-Control-Allow-Origin: *');
}
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_method']]);
    exit();
}

// Get JSON input
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Validate input
if (!$data) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_json']]);
    exit();
}

// Check for honeypot field (spam protection)
if (ENABLE_HONEYPOT && !empty($data['website'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['honeypot_triggered']]);
    exit();
}

// Required fields validation
$required_fields = ['name', 'email', 'company'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => MESSAGES['missing_field'] . ": $field"]);
        exit();
    }
}

// Sanitize and validate input data
$name = sanitizeInput($data['name'], MAX_NAME_LENGTH);
$email = filter_var(trim($data['email']), FILTER_SANITIZE_EMAIL);
$company = sanitizeInput($data['company'], MAX_COMPANY_LENGTH);
$message = sanitizeInput($data['message'] ?? '', MAX_MESSAGE_LENGTH);
$type = sanitizeInput($data['type'] ?? 'message'); // 'message' or 'callback'

// Validation checks
if ($name === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['name_too_long']]);
    exit();
}

if ($company === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['company_too_long']]);
    exit();
}

if ($message === false) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['message_too_long']]);
    exit();
}

if (!validateEmail($email)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => MESSAGES['invalid_email']]);
    exit();
}

// Validate submission type
if (!in_array($type, ['message', 'callback'])) {
    $type = 'message'; // Default to message if invalid type
}

// Create email subject and template based on type
$subject = createEmailSubject($name, $company, $type);

if ($type === 'callback') {
    $html_body = createCallbackEmailTemplate($name, $email, $company, $message);
} else {
    $html_body = createMessageEmailTemplate($name, $email, $company, $message);
}

// Create plain text version
$text_body = "
NEW CONTACT FORM SUBMISSION - Attrahent Technologies
==================================================

Name: $name
Email: $email
Company: $company
Message: " . ($message ?: 'No message provided') . "

Submitted: " . date('Y-m-d H:i:s T') . "
Source: Attrahent Technologies Contact Form
Reply to: $email

--
This email was sent from the Attrahent Technologies website contact form.
";

// Email headers
$headers = [
    'MIME-Version: 1.0',
    'Content-Type: text/html; charset=UTF-8',
    'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
    "Reply-To: $name <$email>",
    'X-Mailer: PHP/' . phpversion(),
    'X-Priority: 3',
    'X-MSMail-Priority: Normal',
    'X-Originating-IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown')
];

// Attempt to send email
try {
    $mail_sent = mail(RECIPIENT_EMAIL, $subject, $html_body, implode("\r\n", $headers));

    if ($mail_sent) {
        // Log successful submission with type
        error_log("Contact form submission ($type): $name ($email) from $company at " . date('Y-m-d H:i:s'));

        // Return success response
        $successMessage = $type === 'callback' ?
            'Callback request sent successfully! We will contact you within 4 hours.' :
            'Message sent successfully!';

        echo json_encode([
            'success' => true,
            'message' => $successMessage,
            'timestamp' => date('c'),
            'type' => $type,
            'data' => [
                'name' => $name,
                'email' => $email,
                'company' => $company,
                'submission_type' => $type
            ]
        ]);
    } else {
        throw new Exception('Mail function returned false');
    }

} catch (Exception $e) {
    // Log error with type
    error_log("Failed to send contact form email ($type): $name ($email) - " . $e->getMessage());

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => MESSAGES['mail_failed'],
        'error' => 'Mail delivery failed',
        'type' => $type,
        'timestamp' => date('c')
    ]);
}
?>
