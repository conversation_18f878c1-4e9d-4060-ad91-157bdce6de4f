import { contactConfig } from '../config/contact';

export interface ContactFormData {
  name: string;
  email: string;
  company: string;
  message: string;
  type?: 'message' | 'callback'; // Optional submission type
}

export interface EmailResponse {
  success: boolean;
  message: string;
  error?: string;
}

// Email template for the contact form submission
const createEmailTemplate = (formData: ContactFormData): string => {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>New Contact Form Submission - Attrahent Technologies</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
        .field { margin-bottom: 15px; }
        .label { font-weight: bold; color: #555; }
        .value { margin-top: 5px; padding: 10px; background: white; border-radius: 4px; border-left: 4px solid #667eea; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>🚀 New Contact Form Submission</h2>
            <p>Attrahent Technologies Website</p>
        </div>
        <div class="content">
            <div class="field">
                <div class="label">👤 Name:</div>
                <div class="value">${formData.name}</div>
            </div>
            <div class="field">
                <div class="label">📧 Email:</div>
                <div class="value">${formData.email}</div>
            </div>
            <div class="field">
                <div class="label">🏢 Company:</div>
                <div class="value">${formData.company}</div>
            </div>
            <div class="field">
                <div class="label">💬 Message:</div>
                <div class="value">${formData.message || 'No message provided'}</div>
            </div>
            <div class="footer">
                <p><strong>📅 Submitted:</strong> ${new Date().toLocaleString()}</p>
                <p><strong>🌐 Source:</strong> Attrahent Technologies Contact Form</p>
                <p><strong>📧 Reply to:</strong> ${formData.email}</p>
            </div>
        </div>
    </div>
</body>
</html>
  `.trim();
};

// Create plain text version for email clients that don't support HTML
const createPlainTextTemplate = (formData: ContactFormData): string => {
  return `
NEW CONTACT FORM SUBMISSION - Attrahent Technologies
==================================================

Name: ${formData.name}
Email: ${formData.email}
Company: ${formData.company}
Message: ${formData.message || 'No message provided'}

Submitted: ${new Date().toLocaleString()}
Source: Attrahent Technologies Contact Form
Reply to: ${formData.email}
  `.trim();
};

// Email sending service using PHP backend API
export const sendContactEmail = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    // Validate form data
    if (!formData.name || !formData.email || !formData.company) {
      throw new Error('Missing required fields');
    }

    // Prepare the data with default type if not specified
    const emailData = {
      name: formData.name.trim(),
      email: formData.email.trim(),
      company: formData.company.trim(),
      message: formData.message?.trim() || '',
      type: formData.type || 'message', // Default to 'message' if not specified
    };

    // Send to PHP backend API
    const response = await fetch('/api/contact.php', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        message: result.message || 'Email sent successfully!',
      };
    } else {
      return {
        success: false,
        message: 'Failed to send email',
        error: result.message || 'Unknown error occurred',
      };
    }

  } catch (error) {
    console.error('❌ Email sending failed:', error);
    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Network error occurred'
    };
  }
};

// Alternative implementation using EmailJS (uncomment to use)
/*
import emailjs from '@emailjs/browser';

export const sendContactEmailWithEmailJS = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    // Initialize EmailJS with your public key
    emailjs.init('YOUR_PUBLIC_KEY');

    const templateParams = {
      to_email: contactConfig.email.testEmail,
      from_name: formData.name,
      from_email: formData.email,
      company: formData.company,
      message: formData.message,
      reply_to: formData.email,
    };

    const result = await emailjs.send(
      'YOUR_SERVICE_ID',
      'YOUR_TEMPLATE_ID',
      templateParams
    );

    return {
      success: true,
      message: 'Email sent successfully!'
    };

  } catch (error) {
    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};
*/
