export interface ContactFormData {
  name: string;
  email: string;
  company: string;
  message: string;
  type?: 'message' | 'callback';
}

export interface EmailResponse {
  success: boolean;
  message: string;
  error?: string;
}

const API_BASE_URL = `${import.meta.env.VITE_API_BASE_URL || ''}/api/contact.php`;

export const sendContactEmail = async (formData: ContactFormData): Promise<EmailResponse> => {
  try {
    // Validate form data
    if (!formData.name || !formData.email || !formData.company) {
      return {
        success: false,
        message: 'Missing required fields',
        error: 'Name, email, and company are required.',
      };
    }

    const emailData = {
      name: formData.name.trim(),
      email: formData.email.trim(),
      company: formData.company.trim(),
      message: formData.message?.trim() || '',
      type: formData.type || 'message',
    };

    // Send to PHP backend API
    const response = await fetch(API_BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const result = await response.json();

    if (result.success) {
      return {
        success: true,
        message: result.message || 'Email sent successfully!',
      };
    } else {
      return {
        success: false,
        message: 'Failed to send email',
        error: result.message || 'Unknown error occurred',
      };
    }
  } catch (error) {
    console.error('❌ Email sending failed:', error);
    return {
      success: false,
      message: 'Failed to send email',
      error: error instanceof Error ? error.message : 'Network error occurred',
    };
  }
};
