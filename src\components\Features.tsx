import React from 'react';
import { motion } from 'framer-motion';
import { Code, Smartphone, Cloud, Zap, Brain, Palette, ArrowRight } from 'lucide-react';

const Features: React.FC = () => {
  const features = [
    {
      icon: Code,
      title: 'Web Development',
      description: 'Launch scalable web platforms, fast.',
      technologies: 'Node.js, ReactJS, Angular, Next.js',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Smartphone,
      title: 'Mobile Apps',
      description: 'Accelerate mobile innovation.',
      technologies: 'React Native, Flutter',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Cloud,
      title: 'Cloud & DevOps',
      description: 'Scale globally with secure, high-performance cloud solutions.',
      technologies: 'AWS, Azure, Microservices',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: Zap,
      title: 'No-Code & Automation',
      description: 'Streamline operations and boost productivity with intelligent automation.',
      technologies: 'RPA, Zapier, Odoo',
      color: 'from-yellow-500 to-orange-500',
    },
    {
      icon: <PERSON>,
      title: 'AI & Chatbots',
      description: 'Automate business workflows with AI.',
      technologies: 'AI Agents, Conversational Bots',
      color: 'from-indigo-500 to-blue-500',
    },
    {
      icon: Palette,
      title: 'UI/UX Design',
      description: 'Create intuitive, user-centered digital experiences.',
      technologies: 'Figma, Design Systems',
      color: 'from-pink-500 to-rose-500',
    },
  ];

  return (
    <section id="features" className="py-24 bg-secondary-50 dark:bg-secondary-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Our Expertise
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            From MVP to enterprise-grade platforms, we deliver end-to-end digital solutions.
          </p>
        </motion.div>

        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{
                y: -10,
                transition: { duration: 0.2 },
              }}
              className="group relative"
            >
              {/* Glassmorphism card */}
              <div className="relative bg-white/70 dark:bg-secondary-800/70 backdrop-blur-sm border border-white/20 dark:border-secondary-700/50 rounded-2xl p-4 sm:p-6 lg:p-8 h-full transition-all duration-300 group-hover:shadow-2xl group-hover:shadow-primary-500/10">
                {/* Icon with gradient background */}
                <div className="relative mb-4 sm:mb-6">
                  <div
                    className={`w-12 h-12 sm:w-14 sm:h-14 lg:w-16 lg:h-16 rounded-xl sm:rounded-2xl bg-gradient-to-br ${feature.color} p-3 sm:p-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <feature.icon className="w-full h-full text-white" />
                  </div>

                  {/* Floating micro-interaction element */}
                  <motion.div
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut',
                      delay: index * 0.2,
                    }}
                    className={`absolute -top-1 -right-1 w-4 h-4 rounded-full bg-gradient-to-br ${feature.color} opacity-60`}
                  />
                </div>

                <h3 className="text-lg sm:text-xl font-bold text-secondary-900 dark:text-white mb-2 sm:mb-4 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                  {feature.title}
                </h3>

                <p className="text-sm sm:text-base text-secondary-600 dark:text-secondary-300 leading-relaxed mb-3 sm:mb-4">
                  {feature.description}
                </p>

                <div className="text-xs sm:text-sm text-primary-600 dark:text-primary-400 font-medium">
                  {feature.technologies}
                </div>

                {/* Hover effect overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary-500/5 to-accent-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
            </motion.div>
          ))}
        </div>

        {/* Integration showcase - Hidden for now */}
        {/* <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-20 text-center"
        >
          <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-8">
            Seamless Integration Ecosystem
          </h3>

          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60 dark:opacity-40">
            {/* Placeholder for client logos */}
            {/* {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                whileHover={{ scale: 1.1, opacity: 1 }}
                className="w-24 h-12 bg-secondary-200 dark:bg-secondary-700 rounded-lg flex items-center justify-center"
              >
                <div className="w-16 h-6 bg-secondary-300 dark:bg-secondary-600 rounded" />
              </motion.div>
            ))}
          </div>
        </motion.div> */}
      </div>
    </section>
  );
};

export default Features;
