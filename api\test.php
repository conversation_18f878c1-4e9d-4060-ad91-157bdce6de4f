<?php
// Simple test script for the contact API
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #667eea; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #5a67d8; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 Contact API Test</h1>
    <p>Test the contact form API endpoint</p>
    
    <form id="testForm">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required value="John Doe">
        </div>
        
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="company">Company *</label>
            <input type="text" id="company" name="company" required value="Test Company Inc.">
        </div>
        
        <div class="form-group">
            <label for="message">Message</label>
            <textarea id="message" name="message" rows="4">This is a test message from the API test page.</textarea>
        </div>
        
        <!-- Honeypot field (should be hidden in real form) -->
        <div class="form-group" style="display: none;">
            <label for="website">Website (leave empty)</label>
            <input type="text" id="website" name="website">
        </div>
        
        <button type="submit">Send Test Email</button>
    </form>
    
    <div id="result"></div>
    
    <hr style="margin: 40px 0;">
    
    <h2>📋 API Information</h2>
    <ul>
        <li><strong>Endpoint:</strong> <code>contact.php</code></li>
        <li><strong>Method:</strong> POST</li>
        <li><strong>Content-Type:</strong> application/json</li>
        <li><strong>Recipient:</strong> <EMAIL></li>
        <li><strong>Required Fields:</strong> name, email, company</li>
        <li><strong>Optional Fields:</strong> message</li>
    </ul>
    
    <h3>📝 Sample Request</h3>
    <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
POST /api/contact.php
Content-Type: application/json

{
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "Test Company Inc.",
    "message": "This is a test message."
}
    </pre>
    
    <h3>✅ Sample Success Response</h3>
    <pre style="background: #d4edda; padding: 15px; border-radius: 4px; overflow-x: auto;">
{
    "success": true,
    "message": "Email sent successfully!",
    "timestamp": "2024-01-15T10:30:00+00:00",
    "data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "company": "Test Company Inc."
    }
}
    </pre>
    
    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            const formData = new FormData(this);
            
            // Convert FormData to JSON
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // Show loading state
            resultDiv.innerHTML = '<div class="result loading">🔄 Sending test email...</div>';
            
            try {
                const response = await fetch('contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ Success!</h3>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Timestamp:</strong> ${result.timestamp}</p>
                            <p><strong>Data:</strong> ${JSON.stringify(result.data, null, 2)}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Error</h3>
                            <p><strong>Message:</strong> ${result.message}</p>
                            ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the API endpoint is accessible and CORS is properly configured.</p>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
