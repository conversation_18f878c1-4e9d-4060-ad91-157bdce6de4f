<?php
// Simple test script for the contact API
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact API Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #667eea; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #5a67d8; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .loading { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🧪 Contact API Test</h1>
    <p>Test the contact form API endpoint with different submission types</p>

    <form id="testForm">
        <div class="form-group">
            <label for="type">Submission Type *</label>
            <select id="type" name="type" required onchange="updateFormForType()">
                <option value="message">📧 Send Message (General Inquiry)</option>
                <option value="callback">📞 Request Callback (Urgent)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required value="John Doe">
        </div>

        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required value="<EMAIL>">
        </div>

        <div class="form-group">
            <label for="company">Company *</label>
            <input type="text" id="company" name="company" required value="Test Company Inc.">
        </div>

        <div class="form-group">
            <label for="message" id="messageLabel">Message</label>
            <textarea id="message" name="message" rows="4" placeholder="Enter your message or additional notes...">This is a test message from the API test page.</textarea>
        </div>

        <!-- Honeypot field (should be hidden in real form) -->
        <div class="form-group" style="display: none;">
            <label for="website">Website (leave empty)</label>
            <input type="text" id="website" name="website">
        </div>

        <button type="submit" id="submitBtn">📧 Send Test Message</button>
    </form>

    <div id="result"></div>

    <hr style="margin: 40px 0;">

    <h2>📋 API Information</h2>
    <ul>
        <li><strong>Endpoint:</strong> <code>contact.php</code></li>
        <li><strong>Method:</strong> POST</li>
        <li><strong>Content-Type:</strong> application/json</li>
        <li><strong>Recipient:</strong> <EMAIL></li>
        <li><strong>Required Fields:</strong> name, email, company</li>
        <li><strong>Optional Fields:</strong> message, type</li>
        <li><strong>Submission Types:</strong>
            <ul>
                <li><code>message</code> - General inquiry (default)</li>
                <li><code>callback</code> - Urgent callback request</li>
            </ul>
        </li>
    </ul>

    <h3>📝 Sample Request - Message</h3>
    <pre style="background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto;">
POST /api/contact.php
Content-Type: application/json

{
    "type": "message",
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "Test Company Inc.",
    "message": "This is a general inquiry."
}
    </pre>

    <h3>📞 Sample Request - Callback</h3>
    <pre style="background: #fff3cd; padding: 15px; border-radius: 4px; overflow-x: auto;">
POST /api/contact.php
Content-Type: application/json

{
    "type": "callback",
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "company": "Important Client Corp",
    "message": "Urgent project discussion needed."
}
    </pre>

    <h3>✅ Sample Success Response</h3>
    <pre style="background: #d4edda; padding: 15px; border-radius: 4px; overflow-x: auto;">
{
    "success": true,
    "message": "Email sent successfully!",
    "timestamp": "2024-01-15T10:30:00+00:00",
    "data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "company": "Test Company Inc."
    }
}
    </pre>

    <script>
        function updateFormForType() {
            const typeSelect = document.getElementById('type');
            const messageLabel = document.getElementById('messageLabel');
            const messageField = document.getElementById('message');
            const submitBtn = document.getElementById('submitBtn');

            if (typeSelect.value === 'callback') {
                messageLabel.textContent = 'Additional Notes (Optional)';
                messageField.placeholder = 'Any specific requirements or preferred callback time...';
                messageField.value = 'Please call me to discuss our upcoming project requirements.';
                submitBtn.innerHTML = '📞 Request Test Callback';
                submitBtn.style.background = '#f5576c';
            } else {
                messageLabel.textContent = 'Message';
                messageField.placeholder = 'Enter your message or additional notes...';
                messageField.value = 'This is a test message from the API test page.';
                submitBtn.innerHTML = '📧 Send Test Message';
                submitBtn.style.background = '#667eea';
            }
        }

        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const resultDiv = document.getElementById('result');
            const formData = new FormData(this);

            // Convert FormData to JSON
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Show loading state with type-specific message
            const loadingMessage = data.type === 'callback' ?
                '📞 Sending callback request...' :
                '📧 Sending test message...';
            resultDiv.innerHTML = `<div class="result loading">🔄 ${loadingMessage}</div>`;

            try {
                const response = await fetch('contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    const successIcon = result.type === 'callback' ? '📞' : '📧';
                    const typeLabel = result.type === 'callback' ? 'Callback Request' : 'Message';

                    resultDiv.innerHTML = `
                        <div class="result success">
                            <h3>${successIcon} Success!</h3>
                            <p><strong>Type:</strong> ${typeLabel}</p>
                            <p><strong>Message:</strong> ${result.message}</p>
                            <p><strong>Timestamp:</strong> ${result.timestamp}</p>
                            <details>
                                <summary><strong>Response Data:</strong></summary>
                                <pre>${JSON.stringify(result.data, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ Error</h3>
                            <p><strong>Message:</strong> ${result.message}</p>
                            ${result.error ? `<p><strong>Error:</strong> ${result.error}</p>` : ''}
                            ${result.type ? `<p><strong>Type:</strong> ${result.type}</p>` : ''}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Network Error</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the API endpoint is accessible and CORS is properly configured.</p>
                    </div>
                `;
            }
        });

        // Initialize form on page load
        updateFormForType();
    </script>
</body>
</html>
