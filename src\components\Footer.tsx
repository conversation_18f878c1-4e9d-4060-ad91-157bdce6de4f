import React from 'react';
import { motion } from 'framer-motion';
import {
  Mail,
  ExternalLink,
  MessageCircle,
  ArrowUp
} from 'lucide-react';
import Logo from './Logo';
import { contactConfig } from '../config/contact';

const Footer: React.FC = () => {
  // Smooth scroll to section
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // Scroll to top of page
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Footer link configuration with proper actions and icons
  const footerLinks = {
    Company: [
      {
        name: 'Solutions',
        action: () => scrollToSection('solutions'),
        type: 'scroll' as const,
        icon: null
      },
      {
        name: 'Services',
        action: () => scrollToSection('features'),
        type: 'scroll' as const,
        icon: null
      },
      {
        name: 'Case Studies',
        action: () => scrollToSection('testimonials'),
        type: 'scroll' as const,
        icon: null
      },
      {
        name: 'Contact',
        action: () => scrollToSection('contact'),
        type: 'scroll' as const,
        icon: null
      }
    ],
    Connect: [
      {
        name: 'WhatsApp',
        action: () => window.open(contactConfig.quickContact.whatsapp.href, '_blank'),
        type: 'external' as const,
        icon: MessageCircle
      },
      {
        name: 'Email Us',
        action: () => window.open(contactConfig.quickContact.email.href, '_self'),
        type: 'email' as const,
        icon: Mail
      }
    ]
  };

  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12 justify-between">
            {/* Company info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-1"
            >
              <div className="mb-6">
                <motion.button
                  onClick={scrollToTop}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-transparent border-none p-0 cursor-pointer group"
                  aria-label="Scroll to top"
                  title="Click to scroll to top"
                >
                  <Logo />
                </motion.button>
              </div>

              <p className="text-secondary-300 mb-6 leading-relaxed">
                Your trusted global software partner for custom web, mobile, cloud, and AI solutions.
                Helping ambitious startups and SMEs build, scale, and transform.
              </p>

              {/* Contact info */}
              <div className="space-y-3">
                <a
                  href={contactConfig.quickContact.email.href}
                  className="flex items-center text-secondary-300 hover:text-primary-400 transition-colors duration-200"
                >
                  <Mail className="w-5 h-5 mr-3 text-primary-400" />
                  {contactConfig.quickContact.email.display}
                </a>
              </div>

              {/* Social links - Hidden for now */}
              {/* <div className="flex space-x-4 mt-6">
                {[
                  { icon: Linkedin, href: contactConfig.social.linkedin },
                  { icon: MessageCircle, href: contactConfig.quickContact.whatsapp.href },
                  { icon: Github, href: contactConfig.social.github }
                ].map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.href}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-secondary-800 hover:bg-primary-600 rounded-lg transition-colors duration-200"
                  >
                    <social.icon className="w-5 h-5" />
                  </motion.a>
                ))}
              </div> */}
            </motion.div>

            {/* Footer links */}
            <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-12">
              {Object.entries(footerLinks).map(([category, links], categoryIndex) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
              >
                <h3 className="font-semibold text-white mb-6">{category}</h3>
                <ul className="space-y-3">
                  {links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <motion.button
                        onClick={link.action}
                        whileHover={{ x: 5 }}
                        whileTap={{ scale: 0.98 }}
                        className="flex items-center text-secondary-300 hover:text-primary-400 transition-colors duration-200 text-left cursor-pointer bg-transparent border-none p-0 font-inherit group"
                      >
                        {link.icon && (
                          <link.icon className="w-4 h-4 mr-2 text-primary-400 group-hover:text-primary-300 transition-colors duration-200" />
                        )}
                        {link.name}
                        {link.type === 'external' && (
                          <ExternalLink className="w-3 h-3 ml-1 opacity-60 group-hover:opacity-100 transition-opacity duration-200" />
                        )}
                      </motion.button>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-secondary-800 py-8">
          <div className="flex justify-between items-center">
            {/* Copyright */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              className="text-secondary-400 text-sm"
            >
              © 2025 Attrahent Technologies. All rights reserved.
            </motion.div>

            {/* Scroll to top button */}
            <motion.button
              onClick={scrollToTop}
              whileHover={{ scale: 1.1, y: -2 }}
              whileTap={{ scale: 0.9 }}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              className="flex items-center justify-center w-10 h-10 bg-primary-600 hover:bg-primary-500 text-white rounded-full transition-colors duration-200 group"
              aria-label="Scroll to top"
              title="Back to top"
            >
              <ArrowUp className="w-5 h-5 group-hover:animate-bounce" />
            </motion.button>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
