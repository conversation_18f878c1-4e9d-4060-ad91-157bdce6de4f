import React from 'react';
import { motion } from 'framer-motion';
import {
  Mail
} from 'lucide-react';
import Logo from './Logo';

const Footer: React.FC = () => {
  const footerLinks = {
    Services: [
      'Web Development',
      'Mobile Apps',
      'Cloud & DevOps',
      'AI & Chatbots',
      'UI/UX Design'
    ],
    Company: [
      'Case Studies',
      'About',
      'Contact'
    ],
    Connect: [
      'LinkedIn',
      'WhatsApp',
      'Email Us',
      'Schedule a Call',
      'Request Quote'
    ]
  };



  return (
    <footer className="bg-secondary-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="py-16">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
            {/* Company info */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-1"
            >
              <div className="mb-6">
                <Logo />
              </div>

              <p className="text-secondary-300 mb-6 leading-relaxed">
                Your trusted global software partner for custom web, mobile, cloud, and AI solutions.
                Helping ambitious startups and SMEs build, scale, and transform.
              </p>

              {/* Contact info */}
              <div className="space-y-3">
                <div className="flex items-center text-secondary-300">
                  <Mail className="w-5 h-5 mr-3 text-primary-400" />
                  <EMAIL>
                </div>
              </div>

              {/* Social links - Hidden for now */}
              {/* <div className="flex space-x-4 mt-6">
                {[
                  { icon: Linkedin, href: 'https://linkedin.com/company/attrahent' },
                  { icon: MessageCircle, href: 'https://wa.me/15551234567' },
                  { icon: Github, href: 'https://github.com/attrahent' }
                ].map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.href}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                    className="p-2 bg-secondary-800 hover:bg-primary-600 rounded-lg transition-colors duration-200"
                  >
                    <social.icon className="w-5 h-5" />
                  </motion.a>
                ))}
              </div> */}
            </motion.div>

            {/* Footer links */}
            {Object.entries(footerLinks).map(([category, links], categoryIndex) => (
              <motion.div
                key={category}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: categoryIndex * 0.1 }}
              >
                <h3 className="font-semibold text-white mb-6">{category}</h3>
                <ul className="space-y-3">
                  {links.map((link, linkIndex) => (
                    <li key={linkIndex}>
                      <motion.a
                        href="#"
                        whileHover={{ x: 5 }}
                        className="text-secondary-300 hover:text-primary-400 transition-colors duration-200"
                      >
                        {link}
                      </motion.a>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-secondary-800 py-8">
          <div className="flex justify-center items-center">
            {/* Copyright */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              className="text-secondary-400 text-sm"
            >
              © 2025 Attrahent Technologies. All rights reserved.
            </motion.div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
