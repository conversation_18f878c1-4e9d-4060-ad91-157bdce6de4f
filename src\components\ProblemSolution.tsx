import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>ertTriangle, Target, TrendingUp, Users, Clock, Shield } from 'lucide-react';

const ProblemSolution: React.FC = () => {
  const problems = [
    {
      icon: AlertTriangle,
      title: 'Legacy System Constraints',
      description: 'Outdated infrastructure limiting growth and innovation potential',
    },
    {
      icon: Clock,
      title: 'Slow Time-to-Market',
      description: 'Extended development cycles delaying competitive advantage',
    },
    {
      icon: Users,
      title: 'Scalability Challenges',
      description: "Systems that can't adapt to growing business demands",
    },
  ];

  const solutions = [
    {
      icon: Target,
      title: 'Modern Architecture',
      description: 'Cloud-native solutions built for performance and flexibility',
    },
    {
      icon: TrendingUp,
      title: 'Rapid Deployment',
      description: 'Accelerated development with proven methodologies',
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-grade security with compliance built-in',
    },
  ];

  return (
    <section id="solutions" className="py-24 bg-white dark:bg-secondary-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Solving Complex Business Challenges
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            We understand the pain points that hold businesses back and deliver solutions that drive
            real results.
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Problems */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-4">
                Common Challenges
              </h3>
              <p className="text-secondary-600 dark:text-secondary-300">
                Organizations face increasing pressure to innovate while maintaining operational
                excellence.
              </p>
            </div>

            <div className="space-y-6">
              {problems.map((problem, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex items-start space-x-4 p-6 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-100 dark:border-red-800/30"
                >
                  <div className="flex-shrink-0">
                    <problem.icon className="w-6 h-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-secondary-900 dark:text-white mb-2">
                      {problem.title}
                    </h4>
                    <p className="text-secondary-600 dark:text-secondary-300">
                      {problem.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Solutions */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-4">
                Our Solutions
              </h3>
              <p className="text-secondary-600 dark:text-secondary-300">
                Comprehensive technology solutions designed to overcome these challenges and drive
                growth.
              </p>
            </div>

            <div className="space-y-6">
              {solutions.map((solution, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 + 0.3 }}
                  whileHover={{ scale: 1.02 }}
                  className="flex items-start space-x-4 p-6 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-100 dark:border-green-800/30 cursor-pointer"
                >
                  <div className="flex-shrink-0">
                    <solution.icon className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-secondary-900 dark:text-white mb-2">
                      {solution.title}
                    </h4>
                    <p className="text-secondary-600 dark:text-secondary-300">
                      {solution.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Data visualization placeholder */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 p-8 bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950/50 dark:to-accent-950/50 rounded-2xl"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-4">
              Proven Results
            </h3>
            <p className="text-secondary-600 dark:text-secondary-300">
              Our solutions deliver measurable impact across key business metrics
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              { metric: '85%', label: 'Faster Deployment' },
              { metric: '60%', label: 'Cost Reduction' },
              { metric: '99.9%', label: 'System Uptime' },
            ].map((stat, index) => (
              <motion.div
                key={index}
                initial={{ scale: 0 }}
                whileInView={{ scale: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 + 0.5 }}
                className="text-center"
              >
                <div className="text-4xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                  {stat.metric}
                </div>
                <div className="text-secondary-600 dark:text-secondary-300 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProblemSolution;
