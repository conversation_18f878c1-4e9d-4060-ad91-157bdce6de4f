import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Mail, ArrowRight, CheckCircle, MessageCircle, Phone, User, Building } from 'lucide-react';
import { contactConfig } from '../config/contact';
import { sendContactEmail, type ContactFormData } from '../services/emailService';

const Newsletter: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: '',
  });
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) return 'Please enter your name';
    if (!formData.email.trim()) return 'Please enter your email';
    if (!formData.company.trim()) return 'Please enter your company name';

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) return 'Please enter a valid email address';

    return null;
  };

  const sendEmail = async (formData: ContactFormData) => {
    // Send actual email using the email service
    const result = await sendContactEmail(formData);

    if (!result.success) {
      throw new Error(result.error || 'Failed to send email. Please try again.');
    }

    return result;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset previous errors
    setSubmitError(null);

    // Validate form
    const validationError = validateForm();
    if (validationError) {
      setSubmitError(validationError);
      return;
    }

    setIsSubmitting(true);

    try {
      // Send email with form data
      const emailData: ContactFormData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        company: formData.company.trim(),
        message: formData.message.trim()
      };

      await sendEmail(emailData);

      // Success - show success state
      setIsSubmitted(true);

      // Log success for debugging
      console.log('✅ Contact form submitted successfully:', {
        name: emailData.name,
        email: emailData.email,
        company: emailData.company,
        timestamp: new Date().toISOString()
      });

      // Reset form after 5 seconds
      setTimeout(() => {
        setIsSubmitted(false);
        setFormData({ name: '', email: '', company: '', message: '' });
      }, 5000);

    } catch (error) {
      // Handle error
      console.error('❌ Contact form submission failed:', error);
      setSubmitError(error instanceof Error ? error.message : 'Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section
      id="contact"
      className="py-24 bg-gradient-to-br from-primary-900 via-primary-800 to-secondary-900 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 50, repeat: Infinity, ease: 'linear' }}
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-br from-accent-400/20 to-primary-400/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ rotate: -360 }}
          transition={{ duration: 60, repeat: Infinity, ease: 'linear' }}
          className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-tr from-primary-400/20 to-accent-400/20 rounded-full blur-3xl"
        />
      </div>

      <div className="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          {/* Contact indicator */}
          <motion.div
            initial={{ scale: 0 }}
            whileInView={{ scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white text-sm font-medium mb-8"
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Let's Start the Conversation
          </motion.div>

          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">Ready to Start?</h2>

          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Tell us about your project and let's create something amazing together.
          </p>

          {/* Contact form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="max-w-2xl mx-auto"
          >
            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  {/* Name field */}
                  <div className="relative">
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Your Name"
                      className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                      required
                    />
                    <User className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/60" />
                  </div>

                  {/* Email field */}
                  <div className="relative">
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Your Email"
                      className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                      required
                    />
                    <Mail className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/60" />
                  </div>
                </div>

                {/* Company field */}
                <div className="relative">
                  <input
                    type="text"
                    name="company"
                    value={formData.company}
                    onChange={handleInputChange}
                    placeholder="Company Name"
                    className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent"
                    required
                  />
                  <Building className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/60" />
                </div>

                {/* Message field */}
                <div className="relative">
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Tell us about your project..."
                    rows={4}
                    className="w-full px-6 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-accent-400 focus:border-transparent resize-none"
                  />
                </div>

                {/* Error message */}
                {submitError && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="p-4 bg-red-500/20 border border-red-500/30 rounded-xl text-red-200 text-sm"
                  >
                    {submitError}
                  </motion.div>
                )}

                {/* Action buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <motion.button
                    type="submit"
                    disabled={isSubmitting}
                    whileHover={!isSubmitting ? { scale: 1.05 } : {}}
                    whileTap={!isSubmitting ? { scale: 0.95 } : {}}
                    className={`flex-1 group px-8 py-4 font-semibold rounded-xl transition-all duration-200 flex items-center justify-center ${
                      isSubmitting
                        ? 'bg-accent-400 cursor-not-allowed'
                        : 'bg-accent-500 hover:bg-accent-600'
                    } text-white`}
                  >
                    {isSubmitting ? (
                      <>
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-2"
                        />
                        Sending...
                      </>
                    ) : (
                      <>
                        Send Message
                        <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                      </>
                    )}
                  </motion.button>

                  <motion.button
                    type="button"
                    disabled={isSubmitting}
                    whileHover={!isSubmitting ? { scale: 1.05 } : {}}
                    whileTap={!isSubmitting ? { scale: 0.95 } : {}}
                    className={`flex-1 px-8 py-4 font-semibold rounded-xl transition-all duration-200 flex items-center justify-center border border-white/20 ${
                      isSubmitting
                        ? 'bg-white/5 text-white/50 cursor-not-allowed'
                        : 'bg-white/10 hover:bg-white/20 text-white'
                    }`}
                  >
                    <Phone className="w-5 h-5 mr-2" />
                    Request a Callback
                  </motion.button>
                </div>
              </form>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, ease: "easeOut" }}
                className="text-center py-12"
              >
                {/* Success animation container */}
                <div className="relative mb-8">
                  {/* Animated background circles */}
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 0.2 }}
                    transition={{ duration: 0.8, delay: 0.2 }}
                    className="absolute inset-0 w-32 h-32 mx-auto bg-green-400 rounded-full"
                  />
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 0.8, opacity: 0.1 }}
                    transition={{ duration: 0.8, delay: 0.4 }}
                    className="absolute inset-0 w-32 h-32 mx-auto bg-green-300 rounded-full"
                  />

                  {/* Main success icon */}
                  <motion.div
                    initial={{ scale: 0, rotate: -180 }}
                    animate={{ scale: 1, rotate: 0 }}
                    transition={{
                      duration: 0.8,
                      delay: 0.3,
                      type: "spring",
                      stiffness: 200,
                      damping: 15
                    }}
                    className="relative z-10 w-32 h-32 mx-auto bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="w-16 h-16 text-white" />
                  </motion.div>
                </div>

                {/* Success message */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                  className="space-y-4"
                >
                  <h3 className="text-3xl font-bold text-white">
                    Message Sent Successfully! 🎉
                  </h3>
                  <p className="text-lg text-primary-200 max-w-md mx-auto">
                    Thank you for reaching out! We've received your message and will get back to you within 24 hours.
                  </p>
                </motion.div>

                {/* Animated checkmarks */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                  className="mt-8 space-y-3"
                >
                  {[
                    'Message received and logged',
                    'Notification sent to our team',
                    'Response within 24 hours'
                  ].map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 1 + index * 0.2 }}
                      className="flex items-center justify-center space-x-3 text-primary-200"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.3, delay: 1.2 + index * 0.2 }}
                        className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"
                      >
                        <CheckCircle className="w-3 h-3 text-white" />
                      </motion.div>
                      <span className="text-sm">{item}</span>
                    </motion.div>
                  ))}
                </motion.div>

                {/* Floating particles animation */}
                <div className="absolute inset-0 pointer-events-none">
                  {[...Array(6)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{
                        opacity: 0,
                        scale: 0,
                        x: Math.random() * 400 - 200,
                        y: Math.random() * 300 - 150
                      }}
                      animate={{
                        opacity: [0, 1, 0],
                        scale: [0, 1, 0],
                        y: [0, -50, -100]
                      }}
                      transition={{
                        duration: 2,
                        delay: 1.5 + i * 0.3,
                        repeat: Infinity,
                        repeatDelay: 3
                      }}
                      className="absolute w-2 h-2 bg-green-400 rounded-full"
                    />
                  ))}
                </div>
              </motion.div>
            )}
          </motion.div>

          {/* Alternative contact methods */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-8 text-center"
          >
            <p className="text-primary-200 mb-4">Or reach out directly:</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <motion.a
                href={contactConfig.quickContact.whatsapp.href}
                target="_blank"
                rel="noopener noreferrer"
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg transition-all duration-200"
              >
                <MessageCircle className="w-5 h-5 mr-2" />
                WhatsApp Chat
              </motion.a>

              <motion.a
                href={contactConfig.quickContact.phone.href}
                whileHover={{ scale: 1.05 }}
                className="inline-flex items-center px-6 py-3 bg-white/10 hover:bg-white/20 text-white font-semibold rounded-lg transition-all duration-200 border border-white/20"
              >
                <Phone className="w-5 h-5 mr-2" />
                Call Now: {contactConfig.quickContact.phone.display}
              </motion.a>
            </div>
          </motion.div>

          {/* Trust indicators - Hidden for now */}
          {/* <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-12 flex flex-wrap justify-center items-center gap-8 text-sm text-primary-200"
          >
            <div>📧 Weekly insights</div>
            <div>🔒 Privacy protected</div>
            <div>📊 Industry reports</div>
            <div>🚀 Early access</div>
          </motion.div> */}
        </motion.div>
      </div>
    </section>
  );
};

export default Newsletter;
