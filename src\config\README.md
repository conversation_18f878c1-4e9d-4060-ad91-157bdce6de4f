# Contact Configuration

This directory contains centralized configuration for all contact information used across the Attrahent Technologies website.

## Usage

### Basic Import
```typescript
import { contactConfig } from '../config/contact';
```

### Quick Contact Methods
```typescript
// Email link
<a href={contactConfig.quickContact.email.href}>
  {contactConfig.quickContact.email.display}
</a>

// Phone link
<a href={contactConfig.quickContact.phone.href}>
  {contactConfig.quickContact.phone.display}
</a>

// WhatsApp link
<a href={contactConfig.quickContact.whatsapp.href}>
  WhatsApp Chat
</a>

// LinkedIn link
<a href={contactConfig.quickContact.linkedin.href}>
  {contactConfig.quickContact.linkedin.display}
</a>
```

### Helper Functions
```typescript
import { getContactHref, getContactDisplay, getContactValue } from '../config/contact';

// Get formatted href for links
const emailHref = getContactHref('email'); // "mailto:<EMAIL>"
const phoneHref = getContactHref('phone'); // "tel:+***********"

// Get display text
const emailDisplay = getContactDisplay('email'); // "<EMAIL>"
const phoneDisplay = getContactDisplay('phone'); // "+****************"

// Get raw values
const emailValue = getContactValue('email'); // "<EMAIL>"
```

### Social Media Links
```typescript
// Access social media profiles
const linkedinUrl = contactConfig.social.linkedin;
const twitterUrl = contactConfig.social.twitter;
const githubUrl = contactConfig.social.github;
```

### Business Information
```typescript
// Access business addresses
const headquarters = contactConfig.addresses.headquarters.full;
const ukOffice = contactConfig.addresses.uk.full;

// Access business hours
const hours = contactConfig.businessHours.weekdays;
const timezone = contactConfig.businessHours.timezone;
```

## Updating Contact Information

To update contact information across the entire website, simply modify the values in `src/config/contact.ts`. All components that import this configuration will automatically use the updated values.

### Key Configuration Sections:

1. **Email Addresses** - Primary, support, and sales emails
2. **Phone Numbers** - Primary, international, and WhatsApp numbers
3. **Social Media** - LinkedIn, Twitter, GitHub, Facebook profiles
4. **Business Addresses** - Headquarters and international offices
5. **Business Hours** - Operating hours and timezone information
6. **Quick Contact** - Pre-formatted contact methods with href and display values

## Components Using Contact Config

Currently implemented in:
- `Footer.tsx` - Email contact link
- `Newsletter.tsx` - WhatsApp and phone contact buttons
- Social media links (when uncommented)

## Benefits

- **Centralized Management**: Update contact info in one place
- **Consistency**: Ensures all contact information is consistent across the site
- **Type Safety**: TypeScript ensures proper usage of contact data
- **Easy Maintenance**: No need to hunt through multiple files to update contact details
- **Scalability**: Easy to add new contact methods or information
