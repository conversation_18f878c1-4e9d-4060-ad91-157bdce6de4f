import React from 'react';
import { motion } from 'framer-motion';
import { Check, Star, Zap, Crown } from 'lucide-react';

const Pricing: React.FC = () => {
  const plans = [
    {
      name: 'Starter',
      icon: Zap,
      price: '2,999',
      period: 'month',
      description: 'Perfect for growing businesses ready to scale',
      features: [
        'Cloud infrastructure setup',
        'Basic security implementation',
        '24/7 monitoring',
        'Email support',
        'Monthly performance reports',
        'Up to 5 integrations',
      ],
      popular: false,
      color: 'from-blue-500 to-cyan-500',
    },
    {
      name: 'Professional',
      icon: Star,
      price: '7,999',
      period: 'month',
      description: 'Comprehensive solution for established enterprises',
      features: [
        'Everything in Starter',
        'Advanced AI analytics',
        'Custom integrations',
        'Priority support',
        'Weekly strategy sessions',
        'Advanced security features',
        'Performance optimization',
        'Dedicated account manager',
      ],
      popular: true,
      color: 'from-purple-500 to-pink-500',
    },
    {
      name: 'Enterprise',
      icon: Crown,
      price: 'Custom',
      period: 'solution',
      description: 'Tailored solutions for complex requirements',
      features: [
        'Everything in Professional',
        'Custom development',
        'White-label solutions',
        'On-premise deployment',
        '24/7 dedicated support',
        'SLA guarantees',
        'Compliance certifications',
        'Executive consulting',
      ],
      popular: false,
      color: 'from-amber-500 to-orange-500',
    },
  ];

  return (
    <section id="pricing" className="py-24 bg-secondary-50 dark:bg-secondary-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Investment Plans
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            Choose the perfect plan to accelerate your digital transformation journey.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ y: -10 }}
              className={`relative ${plan.popular ? 'lg:scale-105' : ''}`}
            >
              {/* Popular badge */}
              {plan.popular && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5 }}
                  className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10"
                >
                  <div className="bg-gradient-to-r from-primary-500 to-accent-500 text-white px-6 py-2 rounded-full text-sm font-semibold shadow-lg">
                    Most Popular
                  </div>
                </motion.div>
              )}

              {/* Card */}
              <div
                className={`relative bg-white dark:bg-secondary-800 rounded-3xl p-8 shadow-xl border-2 ${
                  plan.popular
                    ? 'border-primary-200 dark:border-primary-800'
                    : 'border-secondary-100 dark:border-secondary-700'
                } h-full`}
              >
                {/* Header */}
                <div className="text-center mb-8">
                  <div
                    className={`inline-flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br ${plan.color} mb-4`}
                  >
                    <plan.icon className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-2">
                    {plan.name}
                  </h3>

                  <p className="text-secondary-600 dark:text-secondary-300 mb-6">
                    {plan.description}
                  </p>

                  {/* Price */}
                  <div className="mb-6">
                    {plan.price === 'Custom' ? (
                      <div className="text-3xl font-bold text-secondary-900 dark:text-white">
                        Custom Pricing
                      </div>
                    ) : (
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-secondary-900 dark:text-white">
                          ${plan.price}
                        </span>
                        <span className="text-secondary-600 dark:text-secondary-400 ml-2">
                          /{plan.period}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <motion.div
                      key={featureIndex}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.4, delay: featureIndex * 0.1 }}
                      className="flex items-center space-x-3"
                    >
                      <div className="flex-shrink-0">
                        <Check className="w-5 h-5 text-green-500" />
                      </div>
                      <span className="text-secondary-700 dark:text-secondary-300">{feature}</span>
                    </motion.div>
                  ))}
                </div>

                {/* CTA Button */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => {
                    if (plan.price === 'Custom') {
                      window.location.href = '#contact';
                    } else {
                      window.location.href = '#get-started';
                    }
                  }}
                  className={`w-full py-4 px-6 rounded-xl font-semibold transition-all duration-200 ${
                    plan.popular
                      ? 'bg-gradient-to-r from-primary-600 to-accent-600 hover:from-primary-700 hover:to-accent-700 text-white shadow-lg'
                      : 'bg-secondary-100 dark:bg-secondary-700 hover:bg-secondary-200 dark:hover:bg-secondary-600 text-secondary-900 dark:text-white'
                  }`}
                >
                  {plan.price === 'Custom' ? 'Contact Sales' : 'Get Started'}
                </motion.button>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Additional info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-16"
        >
          <p className="text-secondary-600 dark:text-secondary-400 mb-4">
            All plans include 30-day money-back guarantee
          </p>
          <div className="flex flex-wrap justify-center items-center gap-8 text-sm text-secondary-500 dark:text-secondary-500">
            <div className="flex items-center">
              <Check className="w-4 h-4 mr-2 text-green-500" />
              No setup fees
            </div>
            <div className="flex items-center">
              <Check className="w-4 h-4 mr-2 text-green-500" />
              Cancel anytime
            </div>
            <div className="flex items-center">
              <Check className="w-4 h-4 mr-2 text-green-500" />
              24/7 support
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Pricing;
