# 📧 Attrahent Technologies Contact API

A comprehensive PHP API for handling contact form submissions with dual email templates for different submission types.

## 🚀 Features

- **Dual Email Templates**: Separate templates for general messages and urgent callback requests
- **Email Sending**: Sends formatted HTML emails to specified recipient
- **Priority Handling**: Different styling and urgency levels for callback requests
- **Input Validation**: Validates required fields and email format
- **Spam Protection**: Honeypot field to prevent spam submissions
- **CORS Support**: Configurable CORS headers for frontend integration
- **Rate Limiting**: Optional rate limiting (configurable)
- **Logging**: Automatic logging of submissions and errors
- **Security**: Input sanitization and validation
- **Responsive Design**: Beautiful HTML email templates with professional styling

## 📁 Files

- `contact.php` - Main API endpoint
- `config.php` - Configuration settings
- `test.php` - Test interface for the API
- `README.md` - This documentation

## ⚙️ Configuration

Edit `config.php` to customize:

```php
// Email Configuration
define('RECIPIENT_EMAIL', '<EMAIL>');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Attrahent Technologies Website');

// CORS Configuration
define('ALLOWED_ORIGINS', [
    'http://localhost:3000',
    'https://attrahent.com'
]);
```

## 🔌 API Endpoint

### POST `/api/contact.php`

**Content-Type:** `application/json`

**Request Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "Test Company Inc.",
    "message": "Optional message text",
    "type": "message"
}
```

**Required Fields:**
- `name` (string, max 100 chars)
- `email` (valid email address)
- `company` (string, max 200 chars)

**Optional Fields:**
- `message` (string, max 2000 chars)
- `type` (string, either "message" or "callback", defaults to "message")

**Submission Types:**
- `message` - General inquiry with standard response time (24 hours)
- `callback` - Urgent callback request with priority response (4 hours)

### Success Response (200)
```json
{
    "success": true,
    "message": "Email sent successfully!",
    "timestamp": "2024-01-15T10:30:00+00:00",
    "data": {
        "name": "John Doe",
        "email": "<EMAIL>",
        "company": "Test Company Inc."
    }
}
```

### Error Response (400/500)
```json
{
    "success": false,
    "message": "Error description",
    "error": "Technical error details",
    "timestamp": "2024-01-15T10:30:00+00:00"
}
```

## 🛡️ Security Features

1. **Input Validation**: All inputs are validated and sanitized
2. **Email Validation**: Proper email format checking
3. **Length Limits**: Maximum length validation for all fields
4. **Honeypot Protection**: Hidden field to catch spam bots
5. **CORS Control**: Configurable allowed origins
6. **Rate Limiting**: Optional request rate limiting
7. **IP Logging**: Request IP addresses are logged

## 🧪 Testing

1. Upload the API files to your web server
2. Visit `test.php` in your browser
3. Fill out the test form and submit
4. Check the email recipient for the test message

**Test URL:** `https://yourdomain.com/api/test.php`

## 📧 Email Templates

The API features two distinct email templates based on submission type:

### 📧 General Message Template
For `type: "message"` submissions:
- **Blue gradient header** with standard branding
- **Standard priority notice** (24-hour response time)
- **Professional layout** with organized fields
- **Contact information** clearly displayed
- **Standard metadata** and reply-to headers

### 📞 Callback Request Template
For `type: "callback"` submissions:
- **Red/pink gradient header** for urgency
- **High priority notice** (4-hour response required)
- **Urgent styling** with callback-specific colors
- **Next steps section** with action items
- **Reference number** for tracking
- **Enhanced metadata** with priority indicators

Both templates include:
- **Professional Design**: Gradient headers with company branding
- **Structured Content**: Organized fields with icons
- **Contact Information**: All form data clearly displayed
- **Metadata**: Submission timestamp, IP address, source
- **Reply-To Header**: Easy to reply directly to the sender

## 🔧 Frontend Integration

### JavaScript/Fetch Example

```javascript
const sendContactForm = async (formData) => {
    try {
        const response = await fetch('/api/contact.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            console.log('Email sent successfully!');
            return result;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Failed to send email:', error);
        throw error;
    }
};

// Usage
sendContactForm({
    name: 'John Doe',
    email: '<EMAIL>',
    company: 'Test Company',
    message: 'Hello world!'
});
```

### React/TypeScript Example

```typescript
interface ContactFormData {
    name: string;
    email: string;
    company: string;
    message?: string;
}

const submitContactForm = async (data: ContactFormData) => {
    const response = await fetch('/api/contact.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        throw new Error('Network error');
    }

    return response.json();
};
```

## 📝 Logs

The API automatically logs:
- Successful submissions to `contact_submissions.log`
- Error details to PHP error log
- IP addresses and timestamps

## 🚨 Troubleshooting

**Common Issues:**

1. **CORS Errors**: Add your domain to `ALLOWED_ORIGINS` in config.php
2. **Mail Not Sending**: Check server mail configuration
3. **Permission Errors**: Ensure PHP has write permissions for logs
4. **JSON Errors**: Verify Content-Type header is set correctly

**Server Requirements:**
- PHP 7.0 or higher
- Mail function enabled
- JSON extension enabled

## 📞 Support

For issues or questions about this API, contact the development team.

**Email:** <EMAIL>
