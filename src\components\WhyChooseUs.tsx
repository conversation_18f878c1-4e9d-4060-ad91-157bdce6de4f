import React from 'react';
import { motion } from 'framer-motion';
import { Users, Zap, Eye, Clock, Settings, CheckCircle } from 'lucide-react';

const WhyChooseUs: React.FC = () => {
  const differentiators = [
    {
      icon: Users,
      title: 'Agile, collaborative teams',
      description:
        'Cross-functional teams that work closely with your stakeholders to ensure alignment and rapid iteration.',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      icon: Zap,
      title: 'Rapid prototyping and MVP launch',
      description:
        'Get to market faster with our proven methodology for building and launching minimum viable products.',
      color: 'from-green-500 to-emerald-500',
    },
    {
      icon: Eye,
      title: 'Transparent project management',
      description:
        'Real-time visibility into project progress with regular updates and clear communication channels.',
      color: 'from-purple-500 to-pink-500',
    },
    {
      icon: Clock,
      title: '24/7 support for global clients',
      description:
        'Round-the-clock support across time zones to ensure your business never stops running.',
      color: 'from-orange-500 to-red-500',
    },
    {
      icon: Settings,
      title: 'Flexible engagement models',
      description:
        'Choose from dedicated teams, project-based delivery, or hybrid models that fit your needs.',
      color: 'from-indigo-500 to-blue-500',
    },
  ];

  return (
    <section className="py-24 bg-white dark:bg-secondary-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
            Why Leading Businesses Choose Attrahent
          </h2>
          <p className="text-xl text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto">
            We combine technical excellence with business acumen to deliver solutions that drive
            real results.
          </p>
        </motion.div>

        {/* Differentiators grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {differentiators.map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <div className="bg-secondary-50 dark:bg-secondary-800 rounded-2xl p-8 h-full hover:shadow-xl transition-all duration-300 group-hover:bg-white dark:group-hover:bg-secondary-700">
                {/* Icon */}
                <div className="relative mb-6">
                  <div
                    className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${item.color} p-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}
                  >
                    <item.icon className="w-full h-full text-white" />
                  </div>

                  {/* Check mark indicator */}
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="w-4 h-4 text-white" />
                  </motion.div>
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-secondary-900 dark:text-white mb-4 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300">
                  {item.title}
                </h3>

                <p className="text-secondary-600 dark:text-secondary-300 leading-relaxed">
                  {item.description}
                </p>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-950/30 dark:to-accent-950/30 rounded-3xl p-8 md:p-12">
            <h3 className="text-2xl font-bold text-secondary-900 dark:text-white mb-4">
              Ready to accelerate your digital transformation?
            </h3>
            <p className="text-secondary-600 dark:text-secondary-300 mb-8 max-w-2xl mx-auto">
              Join hundreds of companies that trust Attrahent to deliver innovative solutions that
              drive growth.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="px-8 py-4 bg-primary-600 hover:bg-primary-700 text-white font-semibold rounded-lg transition-all duration-200"
            >
              Start Your Project Today
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
